const fs = require('fs')
const path = require('path')
const createTestCafe = require('testcafe')
const { getXmindInfo } = require('./xmindParser/index.js')
const { genCases } = require('./genCases/index.js')
const { genFile } = require('./genFile/index.js')
const { genInjectFile } = require('./genInjectFile/index.js')

// 路径常量
const CASE_DIR = path.resolve(process.cwd(), './core/test/case')
const INJECT_DIR = path.resolve(process.cwd(), './core/test/inject')
const XMIND_DIR = path.resolve(process.cwd(), './core/xmind')
let usedPorts = new Set();
let reportData = {};
console.log(process.env)
let curRunner = new Set();
const createTmpDir = () => {
  const tempDirPath = path.resolve(process.cwd(), './tmp')

  // 同步创建目录
  if (!fs.existsSync(tempDirPath)) {
    fs.mkdirSync(tempDirPath, { recursive: true })
    console.log('---------- 创建临时目录 ----------')
  } else {
    console.log('---------- 临时目录已存在 ----------')
  }

  return () => {
    console.log('---------- 删除临时目录 ----------')
    fs.rmSync(tempDirPath, { recursive: true, force: true }) // 使用现代API
  }
}
const getRandomPortPair = () => {
  let port1, port2;
  do {
    port1 = 1337 + Math.floor(Math.random() * 1000);
    port2 = port1 + 1;
  } while (usedPorts.has(port1) || usedPorts.has(port2));

  usedPorts.add(port1);
  usedPorts.add(port2);
  return port1; // 返回起始端口
};



// 生成随机字符串
const generateRandomString = () => {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
  let result = '';
  for (let i = 0; i < 6; i++) {
    result += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return result;
};

// 创建自定义报告器
const createCustomReporter = () => ({
  async reportTaskStart(startTime, userAgents, testCount) {
    reportData = {
      caseList: [],
      userAgent: userAgents[0],
      startTime: startTime,
      totalCases: testCount,
    };
    console.log('开始测试', startTime, userAgents, testCount);
    return reportData;
  },
  async reportFixtureStart(name, path, meta) {
    console.log('开始用例测试', name, path, meta);
  },
  async reportTestDone(name, testRunInfo, meta) {
    console.log('测试完成', name, testRunInfo, meta);
    reportData.caseList.push({
      name,
      status: testRunInfo.errs.length === 0 ? 'success' : 'fail',
      duration: testRunInfo.durationMs
    });
  },
  async reportTaskDone(endTime, passed, warnings, result) {
    console.log('测试完成', endTime, passed, warnings, result);
    reportData.isSuccess = result.failedCount;
  }
});

// 执行测试
const executeTests = async (runner, injectFilePath, reportPath, customReporter, caseFilePath, browser,name) => {
  return await runner
    .clientScripts(injectFilePath)
    .reporter([{ name: 'custom', output: reportPath }, customReporter])
    .src([caseFilePath])
    .browsers([browser])
    .screenshots({
      path: 'screenshots',
      takeOnFails: true,
      thumbnails: false
    })
    .run({
      skipJsErrors: true,
      skipUncaughtErrors: true,
      testExecutionTimeout: 300000,
      runExecutionTimeout: 1200000,
      pageRequestTimeout: 50000,
      developmentMode: true,
      retryTestPages: true,
      disableNativeAutomation: true,
      "quarantineMode": {
        "successThreshold": 1,
        "attemptLimit": 3
      }
    });
};

const start = async (name, browser, groupName, func) => {
  let caseName = name;
  console.log(`---------- 开始读取[${caseName}用例，运行环境${browser}浏览器] ----------`);

  const removeTmpDir = createTmpDir();

  try {
    // 获取 XMind 信息
    const xmindPath = path.join(XMIND_DIR, `${caseName || 't0'}.xmind`);
    const xmindInfo = await getXmindInfo(xmindPath);

    // 生成用例文件
    const caseInfoPath = path.join(CASE_DIR, `${caseName}.json`);
    await genFile(caseInfoPath, JSON.stringify(xmindInfo, null, 2));

    // 生成测试用例
    const caseStr = genCases(xmindInfo);
    const caseFilePath = path.join(CASE_DIR, `${caseName}.js`);
    await genFile(caseFilePath, caseStr);

    // 生成注入文件
    const injectFilePath = path.join(INJECT_DIR, `${caseName}.inject.js`);
    await genInjectFile(xmindInfo, injectFilePath);

    const currentPort = getRandomPortPair();

    // 创建 TestCafe 实例
    const testcafe = await createTestCafe({
      hostname: 'localhost',
      port1: currentPort,
      port2: currentPort + 1,
      retryTestPages: true,
      cache: true,
    });
    const entry = {
      name,
      runner: testcafe.createRunner(),
      testcafe,  // 新增testcafe实例引用
      currentPort // 保存当前使用的端口
    };
    curRunner.add(entry);

    const fileTag = generateRandomString();
    const reportPath = path.resolve(process.cwd(), 'reports', `report-${caseName}-${fileTag}.html`);

    console.log('运行端口', currentPort, currentPort + 1);

    await executeTests(entry.runner, injectFilePath, reportPath, createCustomReporter, caseFilePath, browser,name);
    await testcafe.close();
    reportData = {
      ...reportData,
      browser,
      caseName,
      groupName,
      fileTag,
      port: currentPort
    };
    console.log('---------- 测试结束 ----------', reportData);
    await func(reportData); //发送测试结果
    return reportData;
  } catch (error) {
    console.error('---------- 流程执行异常 ----------');
    await func(error);
    throw error;
  } finally {
    removeTmpDir();
  }
};

const stop = async (name) => {
  const entry = Array.from(curRunner).find(entry => entry.name === name);
  
  if (entry && entry.runner) {
    try {
      console.log(`[${name}] 正在终止测试任务...`);
      
      // 先关闭TestCafe实例再停止runner
      if (entry.testcafe) {
        await entry.testcafe.close();
      }
      
      await entry.runner.stop();
      
      // 增加延迟确保资源释放
      await new Promise(resolve => setTimeout(resolve, 1000));

      // 释放已占用的端口
      usedPorts.delete(entry.currentPort);
      usedPorts.delete(entry.currentPort + 1);
      
      curRunner.delete(entry);
      console.log(`[${name}] 测试任务已终止`);
    } catch (error) {
      console.error(`[${name}] 终止任务失败:`, error);
      // 强制清理残留资源
      if (entry.testcafe) {
        await entry.testcafe.close();
      }
      curRunner.delete(entry);
    }
  }
}
module.exports = { start,stop }


