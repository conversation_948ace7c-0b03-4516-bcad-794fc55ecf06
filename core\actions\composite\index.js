const { datepicker } = require('./datepicker')
const { upload, getUploadFilePath } = require('./upload')
const { setting, quickSetting } = require('./setting')
const { compare } = require('./compare')
const { setWindowName, switchWindow, closeWindow } = require('./window')
const { willAuth } = require('./willAuth')

const compositeActions = {
  datepicker,
  upload,
  getUploadFilePath,
  setting,
  compare,
  quickSetting,
  setWindowName,
  switchWindow,
  closeWindow,
  willAuth,
}

exports.composite = compositeActions