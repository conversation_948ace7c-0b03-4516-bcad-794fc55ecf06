const { ClientFunction } = require('testcafe');

// eslint-disable-next-line no-undef
const setLocalStorageItem = ClientFunction(({ key, value }) => window.localStorage.setItem(key, value));
// eslint-disable-next-line no-undef
const setSessionStorageItem = ClientFunction(({ key, value }) => window.sessionStorage.setItem(key, value));

const mockFN = async (t, mock) => {
  if (mock?.cookies) {
    for (const item of mock.cookies) {
      await t.setCookies({ name: item.key, value: item.value });
    }
  }
  if (mock?.localStorage) {
    for (const item of mock.localStorage) {
      await setLocalStorageItem(item);
    }
  }
  if (mock?.sessionStorage) {
    for (const item of mock.sessionStorage) {
      await setSessionStorageItem(item);
    }
  }

  // 针对模板设置添加额外逻辑 可以使用生成去避免
  await setLocalStorageItem({ key: 'setGuideShow', value: 'true' });
};

exports.mockFN = mockFN