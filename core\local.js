const fs = require('fs')
const path = require('path')
const createTestCafe = require('testcafe')
const monent = require('moment')
const { getXmindInfo } = require('./xmindParser/index.js')
const { genCases } = require('./genCases/index.js')
const { genFile } = require('./genFile/index.js')
const { genInjectFile } = require('./genInjectFile/index.js')
const axios = require('axios')

// 路径常量
const CASE_DIR = path.resolve(process.cwd(), './core/test/case')
const INJECT_DIR = path.resolve(process.cwd(), './core/test/inject')
const XMIND_DIR = path.resolve(process.cwd(), './core/xmind')
let usedPorts = new Set();
console.log(process.env)
const createTmpDir = () => {
  const tempDirPath = path.resolve(process.cwd(), './tmp')

  // 同步创建目录
  if (!fs.existsSync(tempDirPath)) {
    fs.mkdirSync(tempDirPath, { recursive: true })
    console.log('---------- 创建临时目录 ----------')
  } else {
    console.log('---------- 临时目录已存在 ----------')
  }

  return () => {
    console.log('---------- 删除临时目录 ----------')
    fs.rmSync(tempDirPath, { recursive: true, force: true }) // 使用现代API
  }
}
const getRandomPortPair = () => {
  let port1, port2;
  do {
    port1 = 1337 + Math.floor(Math.random() * 1000);
    port2 = port1 + 1;
  } while (usedPorts.has(port1) || usedPorts.has(port2));

  usedPorts.add(port1);
  usedPorts.add(port2);
  return port1; // 返回起始端口
};




const start = async (name, browser) => {
  let caseName = name // 声明在前
  console.log(`---------- 开始读取[${caseName}用例，运行环境${browser}浏览器] ----------`)
  const removeTmpDir = createTmpDir()
  try {
    const xmindPath = path.join(XMIND_DIR, `${caseName || 't0'}.xmind`)
    const xmindInfo = await getXmindInfo(xmindPath)
    const userAgent = 'xmindInfo.userAgent'
    // 生成用例文件
    const caseInfoPath = path.join(CASE_DIR, `${caseName}.json`)
    await genFile(caseInfoPath, JSON.stringify(xmindInfo, null, 2))

    // 生成测试用例
    const caseStr = genCases(xmindInfo)
    const caseFilePath = path.join(CASE_DIR, `${caseName}.js`)
    await genFile(caseFilePath, caseStr)

    // 生成注入文件
    const injectFilePath = path.join(INJECT_DIR, `${caseName}.inject.js`)
    await genInjectFile(xmindInfo, injectFilePath)
    const currentPort = getRandomPortPair(); // 自动同时占用连续两个端口

    // return 123

    // 测试执行
    const testcafe = await createTestCafe({
      hostname: 'localhost',
      port1: currentPort,
      port2: currentPort + 1
    })
    let reportData = {}
    const curTime= Math.floor(new Date().getTime()/1000)
    const customReporter = () => {
      return {
        async reportTaskStart(startTime, userAgents, testCount) {
          reportData = {
            ...reportData,
            caseList: [],
            userAgent: userAgents[0],
            startTime: curTime,
            totalCases: testCount,
          }
          console.log('开始测试', startTime, userAgents, testCount)
        },
        async reportFixtureStart(name, path, meta) {
          console.log('开始用例测试', name, path, meta)
        },
        async reportTestDone(name, testRunInfo, meta) {
          console.log('测试完成', name, testRunInfo, meta)
          reportData.caseList.push({
            name,
            status: testRunInfo.errs.length === 0 ? 'success' : 'fail',
            duration: testRunInfo.durationMs
          })
        },
        async reportTaskDone(endTime, passed, warnings, result) {
          console.log('测试完成', endTime, passed, warnings, result)
          reportData.isSuccess = result.failedCount
        }
      };
    };
    // 报告配置
    const reportPath = path.resolve(process.cwd(), 'reports',
      `report-${caseName}-local.html`)
    // 在创建TestCafe实例后添加验证
    console.log('运行端口', currentPort, currentPort + 1)
    const runner = testcafe.createRunner();

    const errorTimes = await runner
      .clientScripts(injectFilePath)
      .reporter([{ name: 'custom', output: reportPath }, customReporter])
      .src([caseFilePath])
      .browsers([browser])
      .screenshots({
        path: 'screenshots',
        takeOnFails: true,
        thumbnails: false
      })
      .run({
        skipJsErrors: true,
        skipUncaughtErrors: true,
        pageRequestTimeout: 10000,
        disableNativeAutomation: true,
        "quarantineMode": {
          "successThreshold": 1,
          "attemptLimit": 3
        }
      })

    await testcafe.close()
    reportData.browser = browser
    reportData.caseName = caseName
    reportData.port = currentPort
    console.log('---------- 测试结束 ----------', reportData)
    try {
      // await sendResult(reportData)
    } catch (notificationError) {
      console.error('[Critical] Failed to send notification:', notificationError.message);
    }

  } catch (error) {
    console.error('---------- 流程执行异常 ----------')
    console.error(error.stack)
    // await sendResult(reportData)
    throw error // 向上抛出异常
  } finally {
    removeTmpDir() // 确保始终执行清理
  }
}
//{caseName, currentPort, browser, userAgent, startTime, errorTimes,totalCases, status}
const sendResult = async (reportData) => {
  try {
    const notification = {
      timestamp: new Date().toISOString(),
      port: reportData.port,
      caseName: reportData.caseName,
      startTime: reportData.startTime,
      testInfo: JSON.stringify(reportData.caseList),
      browser: reportData.browser,
      duration: (Date.now() - reportData.startTime),
      totalCases: reportData.totalCases,
      isSuccess: reportData.isSuccess,
      userAgent: reportData.userAgent,
      env: process.env.NODE_ENV,
      apiBaseUrl: process.env.API_SERVER_URL
    }
    console.log('[Success] Test finish notification:', notification);
    const notifyUrl = `${process.env.API_SERVER_URL}/notify/finishTest`;
    await axios.post(notifyUrl, notification, {
      headers: {
        'Content-Type': 'application/json',
        'X-Request-Source': 'testcafe-runner'
      }
    });
  } catch (notificationError) {
    console.error('[Critical] Failed to send notification:', notificationError.message);
  }
}
// 执行命令：pnpm run local -- --env=dev -p 3000
// console.log(process.argv);
start(process.argv[2]||'test', process.argv[3]||'chrome')