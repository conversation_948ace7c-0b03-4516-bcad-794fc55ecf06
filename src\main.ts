import { NestFactory } from '@nestjs/core';
import { AppModule } from './app.module';
async function bootstrap() {
  const app = await NestFactory.create(AppModule); 
  app.enableCors(); // 允许跨域
  await app.listen(process.env.PORT ?? 3001);
}

// 添加全局异常捕获
process.on('uncaughtException', (err) => {
  console.error('全局异常:', err);
  // 保持进程存活
  // process.exit(1); // 根据实际情况决定是否退出
});

process.on('unhandledRejection', (reason) => {
  console.error('未处理的Promise拒绝:', reason);
});
bootstrap();
