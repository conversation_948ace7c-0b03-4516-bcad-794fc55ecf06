import { Module } from '@nestjs/common';
import { AppController } from './app.controller';
import { AppService } from './app.service';
import { NotifyModule } from './notify/notify.module'; 
import { ConfigModule } from '@nestjs/config';
import { TypeOrmModule } from '@nestjs/typeorm';
import { TestRecordModule } from 'src/record/record.module';
import { ServeStaticModule } from '@nestjs/serve-static';
import { join } from 'path';
import config from './config/config';
@Module({
  imports: [
    ConfigModule.forRoot({
      isGlobal: true,
      envFilePath: config
    }),
    ServeStaticModule.forRoot({
      rootPath: join('E:/public-ui-test/reporter'), // 本地文件路径
      serveRoot: '/reporter', // 访问路径前缀
    }),
    TypeOrmModule.forRoot({
      type: 'mysql',
      host: process.env.DATABASE_HOST,
      port: Number(process.env.DATABASE_PORT),
      username: process.env.DATABASE_username,
      password: process.env.DATABASE_password,
      database: process.env.DATABASE_database,
      // logging: true,
      autoLoadEntities: true,
      synchronize: true, // TypeORM 不支持某些索引选项和定义，禁止生产环境使用，否则索引数据将会丢失
    }), 
    NotifyModule,
    TestRecordModule
  ],
  controllers: [AppController],
  providers: [AppService],
})
export class AppModule { }
