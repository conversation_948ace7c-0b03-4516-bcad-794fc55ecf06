{"info": {"name": "筛选组件"}, "funcs": [{"title": "新增必填校验不通过并取消", "pd": {"preset": ""}, "steps": [{"text": "点击table-head-add-button", "type": "click", "element": "table-head-add-button"}, {"text": "点击表单项prop1", "type": "click", "element": "表单项prop1"}, {"text": "输入1", "type": "input", "value": "1"}, {"text": "点击表单项prop2", "type": "click", "element": "表单项prop2"}, {"text": "输入2", "type": "input", "value": "2"}, {"text": "点击表单项prop3", "type": "click", "element": "表单项prop3"}, {"text": "输入3", "type": "input", "value": "3"}, {"text": "点击addDialogCancel", "type": "click", "element": "addDialogCancel"}, {"text": "等待1秒", "type": "wait", "value": "1000"}, {"text": "比对节点css://body与未新增.png", "type": "compare", "value": {"node": "css://body", "file": "未新增.png"}}]}, {"title": "新增异常校验不通过并关闭", "pd": {"preset": ""}, "steps": [{"text": "点击table-head-add-button", "type": "click", "element": "table-head-add-button"}, {"text": "点击表单项prop1", "type": "click", "element": "表单项prop1"}, {"text": "输入1", "type": "input", "value": "1"}, {"text": "点击表单项prop2", "type": "click", "element": "表单项prop2"}, {"text": "输入2", "type": "input", "value": "2"}, {"text": "点击表单项prop3", "type": "click", "element": "表单项prop3"}, {"text": "输入3", "type": "input", "value": "3"}, {"text": "点击css://.es-dialog__header .es-dialog__header-button", "type": "click", "element": "css://.es-dialog__header .es-dialog__header-button"}, {"text": "等待1秒", "type": "wait", "value": "1000"}, {"text": "比对节点css://body与未新增.png", "type": "compare", "value": {"node": "css://body", "file": "未新增.png"}}]}, {"title": "新增必填校验不通过并点击确定", "pd": {"preset": ""}, "steps": [{"text": "点击table-head-add-button", "type": "click", "element": "table-head-add-button"}, {"text": "点击表单项prop1", "type": "click", "element": "表单项prop1"}, {"text": "输入1", "type": "input", "value": "1"}, {"text": "点击表单项prop2", "type": "click", "element": "表单项prop2"}, {"text": "输入2", "type": "input", "value": "2"}, {"text": "点击表单项prop3", "type": "click", "element": "表单项prop3"}, {"text": "输入3", "type": "input", "value": "3"}, {"text": "选择确定.button", "type": "choose", "value": "确定.button"}, {"text": "等待1秒", "type": "wait", "value": "1000"}, {"text": "判断css://.es-message的内容包含参数为空", "type": "assert", "element": "css://.es-message", "value": "include.text:参数为空"}, {"text": "比对节点css://body与未新增.png", "type": "compare", "value": {"node": "css://body", "file": "未新增.png"}}]}, {"title": "新增异常校验不通过并点击确定", "pd": {"preset": ""}, "steps": [{"text": "点击table-head-add-button", "type": "click", "element": "table-head-add-button"}, {"text": "点击表单项prop1", "type": "click", "element": "表单项prop1"}, {"text": "输入1", "type": "input", "value": "1"}, {"text": "点击表单项prop2", "type": "click", "element": "表单项prop2"}, {"text": "输入2", "type": "input", "value": "2"}, {"text": "点击表单项prop3", "type": "click", "element": "表单项prop3"}, {"text": "输入3", "type": "input", "value": "3"}, {"text": "选择确定.button", "type": "choose", "value": "确定.button"}, {"text": "等待1秒", "type": "wait", "value": "1000"}, {"text": "判断css://.es-message的内容包含参数为空", "type": "assert", "element": "css://.es-message", "value": "include.text:参数为空"}, {"text": "比对节点css://body与未新增.png", "type": "compare", "value": {"node": "css://body", "file": "未新增.png"}}]}, {"title": "新增校验通过并点击确定", "pd": {"preset": ""}, "steps": [{"text": "点击table-head-add-button", "type": "click", "element": "table-head-add-button"}, {"text": "点击表单项prop1", "type": "click", "element": "表单项prop1"}, {"text": "输入1", "type": "input", "value": "1"}, {"text": "点击表单项prop2", "type": "click", "element": "表单项prop2"}, {"text": "输入2", "type": "input", "value": "2"}, {"text": "点击表单项prop3", "type": "click", "element": "表单项prop3"}, {"text": "输入3", "type": "input", "value": "3"}, {"text": "点击表单项name", "type": "click", "element": "表单项name"}, {"text": "输入呃呃呃呃呃", "type": "input", "value": "呃呃呃呃呃"}, {"text": "选择确定.button", "type": "choose", "value": "确定.button"}, {"text": "等待2秒", "type": "wait", "value": "2000"}, {"text": "比对节点css://body与新增.png", "type": "compare", "value": {"node": "css://body", "file": "新增.png"}}]}, {"title": "查看数据", "pd": {"preset": ""}, "steps": [{"text": "点击按钮详情", "type": "click", "element": "按钮详情"}, {"text": "判断表单项name的内容为呃呃呃呃呃", "type": "assert", "element": "表单项name", "value": "have.text:呃呃呃呃呃"}, {"text": "判断属性表单项name的disabled为disabled", "type": "assertAttr", "element": "表单项name", "attribute": "disabled", "value": "disabled", "isEql": true}, {"text": "判断表单项prop1的内容为1", "type": "assert", "element": "表单项prop1", "value": "have.text:1"}, {"text": "判断属性表单项prop1的disabled为disabled", "type": "assertAttr", "element": "表单项prop1", "attribute": "disabled", "value": "disabled", "isEql": true}, {"text": "判断表单项prop2的内容为2", "type": "assert", "element": "表单项prop2", "value": "have.text:2"}, {"text": "判断属性表单项prop2的disabled为disabled", "type": "assertAttr", "element": "表单项prop2", "attribute": "disabled", "value": "disabled", "isEql": true}, {"text": "判断表单项prop3的内容为3", "type": "assert", "element": "表单项prop3", "value": "have.text:3"}, {"text": "判断属性表单项prop3的disabled为disabled", "type": "assertAttr", "element": "表单项prop3", "attribute": "disabled", "value": "disabled", "isEql": true}, {"text": "点击css://.es-dialog__header .es-dialog__header-button", "type": "click", "element": "css://.es-dialog__header .es-dialog__header-button"}, {"text": "比对节点css://body与新增.png", "type": "compare", "value": {"node": "css://body", "file": "新增.png"}}]}, {"title": "搜索有内容", "pd": {"preset": ""}, "steps": [{"text": "点击css://.filter-item:nth-child(1) .filter-item-context", "type": "click", "element": "css://.filter-item:nth-child(1) .filter-item-context"}, {"text": "输入呃呃呃呃呃", "type": "input", "value": "呃呃呃呃呃"}, {"text": "点击css://.filter-item:nth-child(2) .filter-item-context input", "type": "click", "element": "css://.filter-item:nth-child(2) .filter-item-context input"}, {"text": "输入1", "type": "input", "value": "1"}, {"text": "点击按钮查 询", "type": "click", "element": "按钮查 询"}, {"text": "等待1秒", "type": "wait", "value": "1000"}, {"text": "判断css://.es-table__empty-text不存在", "type": "assert", "element": "css://.es-table__empty-text", "value": "not.exists"}, {"text": "判断css://.filter-item:nth-child(1) .filter-item-context的内容为呃呃呃呃呃", "type": "assert", "element": "css://.filter-item:nth-child(1) .filter-item-context", "value": "have.text:呃呃呃呃呃"}, {"text": "判断css://.filter-item:nth-child(2) .filter-item-context input的内容为1", "type": "assert", "element": "css://.filter-item:nth-child(2) .filter-item-context input", "value": "have.text:1"}]}, {"title": "搜索无内容并重置", "pd": {"preset": ""}, "steps": [{"text": "点击css://.filter-item:nth-child(1) .filter-item-context", "type": "click", "element": "css://.filter-item:nth-child(1) .filter-item-context"}, {"text": "输入好吧", "type": "input", "value": "好吧"}, {"text": "点击css://.filter-item:nth-child(2) .filter-item-context input", "type": "click", "element": "css://.filter-item:nth-child(2) .filter-item-context input"}, {"text": "输入1", "type": "input", "value": "1"}, {"text": "点击按钮查 询", "type": "click", "element": "按钮查 询"}, {"text": "等待1秒", "type": "wait", "value": "1000"}, {"text": "判断css://.es-table__empty-text存在", "type": "assert", "element": "css://.es-table__empty-text", "value": "exists"}, {"text": "点击按钮重 置", "type": "click", "element": "按钮重 置"}, {"text": "等待1秒", "type": "wait", "value": "1000"}, {"text": "判断css://.es-table__empty-text不存在", "type": "assert", "element": "css://.es-table__empty-text", "value": "not.exists"}]}, {"title": "更多存在", "pd": {"preset": ""}, "steps": [{"text": "判断css://.filter-btn .expand-collapse存在", "type": "assert", "element": "css://.filter-btn .expand-collapse", "value": "exists"}, {"text": "判断样式css://.filter-item:nth-child(4)的display为none", "type": "assertStyle", "element": "css://.filter-item:nth-child(4)", "style": "display", "value": "none", "isEql": true}, {"text": "点击css://.filter-btn .expand-collapse", "type": "click", "element": "css://.filter-btn .expand-collapse"}, {"text": "判断样式css://.filter-item:nth-child(4)的display不为none", "type": "assertStyle", "element": "css://.filter-item:nth-child(4)", "style": "display", "value": "none", "isEql": false}]}, {"title": "编辑必填校验不通过并取消", "pd": {"preset": ""}, "steps": [{"text": "点击按钮编辑", "type": "click", "element": "按钮编辑"}, {"text": "点击表单项name", "type": "click", "element": "表单项name"}, {"text": "快捷键 ctrl+a delete", "type": "keypress", "value": " ctrl+a delete"}, {"text": "点击editDialogCancel", "type": "click", "element": "editDialogCancel"}, {"text": "等待1秒", "type": "wait", "value": "1000"}, {"text": "比对节点css://body与新增.png", "type": "compare", "value": {"node": "css://body", "file": "新增.png"}}]}, {"title": "编辑异常校验不通过并取消", "pd": {"preset": ""}, "steps": [{"text": "点击按钮编辑", "type": "click", "element": "按钮编辑"}, {"text": "点击表单项name", "type": "click", "element": "表单项name"}, {"text": "快捷键 ctrl+a delete", "type": "keypress", "value": " ctrl+a delete"}, {"text": "点击css://.es-dialog__header .es-dialog__header-button", "type": "click", "element": "css://.es-dialog__header .es-dialog__header-button"}, {"text": "等待1秒", "type": "wait", "value": "1000"}, {"text": "比对节点css://body与新增.png", "type": "compare", "value": {"node": "css://body", "file": "新增.png"}}]}, {"title": "编辑异常校验不通过并点击关闭", "pd": {"preset": ""}, "steps": [{"text": "点击按钮编辑", "type": "click", "element": "按钮编辑"}, {"text": "点击表单项name", "type": "click", "element": "表单项name"}, {"text": "快捷键 ctrl+a delete", "type": "keypress", "value": " ctrl+a delete"}, {"text": "点击editDialogConfirm", "type": "click", "element": "editDialogConfirm"}, {"text": "等待1秒", "type": "wait", "value": "1000"}, {"text": "判断css://.es-message的内容包含参数为空", "type": "assert", "element": "css://.es-message", "value": "include.text:参数为空"}, {"text": "比对节点css://body与新增.png", "type": "compare", "value": {"node": "css://body", "file": "新增.png"}}]}, {"title": "编辑异常校验不通过并点击关闭", "pd": {"preset": ""}, "steps": [{"text": "点击按钮编辑", "type": "click", "element": "按钮编辑"}, {"text": "点击表单项name", "type": "click", "element": "表单项name"}, {"text": "快捷键 ctrl+a delete", "type": "keypress", "value": " ctrl+a delete"}, {"text": "点击editDialogConfirm", "type": "click", "element": "editDialogConfirm"}, {"text": "等待1秒", "type": "wait", "value": "1000"}, {"text": "判断css://.es-message的内容包含参数为空", "type": "assert", "element": "css://.es-message", "value": "include.text:参数为空"}, {"text": "比对节点css://body与新增.png", "type": "compare", "value": {"node": "css://body", "file": "新增.png"}}]}, {"title": "编辑校验通过并点击确定", "pd": {"preset": ""}, "steps": [{"text": "点击按钮编辑", "type": "click", "element": "按钮编辑"}, {"text": "点击表单项prop3", "type": "click", "element": "表单项prop3"}, {"text": "快捷键 ctrl+a delete", "type": "keypress", "value": " ctrl+a delete"}, {"text": "点击editDialogConfirm", "type": "click", "element": "editDialogConfirm"}, {"text": "等待1秒", "type": "wait", "value": "1000"}, {"text": "比对节点css://body与编辑后.png", "type": "compare", "value": {"node": "css://body", "file": "编辑后.png"}}]}, {"title": "删除数据点击关闭", "pd": {"preset": ""}, "steps": [{"text": "点击按钮删除", "type": "click", "element": "按钮删除"}, {"text": "点击css://.es-message-box__header .es-message-box__close", "type": "click", "element": "css://.es-message-box__header .es-message-box__close"}, {"text": "比对节点css://body与编辑后.png", "type": "compare", "value": {"node": "css://body", "file": "编辑后.png"}}]}, {"title": "删除数据点击取消", "pd": {"preset": ""}, "steps": [{"text": "点击按钮删除", "type": "click", "element": "按钮删除"}, {"text": "点击按钮取消", "type": "click", "element": "按钮取消"}, {"text": "比对节点css://body与编辑后.png", "type": "compare", "value": {"node": "css://body", "file": "编辑后.png"}}]}, {"title": "删除数据点击确定", "pd": {"preset": ""}, "steps": [{"text": "点击按钮删除", "type": "click", "element": "按钮删除"}, {"text": "点击按钮确定", "type": "click", "element": "按钮确定"}, {"text": "等待5秒", "type": "wait", "value": "5000"}, {"text": "比对节点css://body与新增.png", "type": "compare", "value": {"node": "css://body", "file": "新增.png"}}]}], "styles": []}