- config:
    name: 基于审查角色获取合同类型列表测试
    base_url: ${ENV(base_url)}
    variables:
        - app_Id: ${ENV(app_Id)}

- test:
    name: 正常场景-获取法务审查角色的合同类型列表
    variables:
        - params: "reviewRole=LEGAL_REVIEWER"
    api: api/contract-review/settings/review-role-catogory-list.yml
    extract:
        - categoryList: content.categoryList
        - firstCategory: content.categoryList.0.category
        - firstInventoryId: content.categoryList.0.inventoryId
    validate:
        - eq: ["status_code", 200]
        - type_match: ["content.categoryList", list]
        - contains: ["content.categoryList.0", "category"]
        - contains: ["content.categoryList.0", "categoryName"]
        - contains: ["content.categoryList.0", "inventoryStatus"]

- test:
    name: 正常场景-获取HR审查角色的合同类型列表
    variables:
        - params: "reviewRole=HR_REVIEWER"
    api: api/contract-review/settings/review-role-catogory-list.yml
    validate:
        - eq: ["status_code", 200]
        - type_match: ["content.categoryList", list]

- test:
    name: 异常场景-缺少必填参数reviewRole
    variables:
        - params: ""
    api: api/contract-review/settings/review-role-catogory-list.yml
    validate:
        - eq: ["status_code", 400]

- test:
    name: 异常场景-reviewRole参数值为空
    variables:
        - params: "reviewRole="
    api: api/contract-review/settings/review-role-catogory-list.yml
    validate:
        - eq: ["status_code", 400]

- test:
    name: 异常场景-reviewRole参数值无效
    variables:
        - params: "reviewRole=INVALID_ROLE"
    api: api/contract-review/settings/review-role-catogory-list.yml
    validate:
        - eq: ["status_code", 400]

- test:
    name: 异常场景-reviewRole参数类型错误
    variables:
        - params: "reviewRole=123"
    api: api/contract-review/settings/review-role-catogory-list.yml
    validate:
        - eq: ["status_code", 400]

- test:
    name: 边界场景-reviewRole包含特殊字符
    variables:
        - params: "reviewRole=LEGAL@REVIEWER"
    api: api/contract-review/settings/review-role-catogory-list.yml
    validate:
        - eq: ["status_code", 400]

- test:
    name: 边界场景-reviewRole超长字符串
    variables:
        - params: "reviewRole=VERY_LONG_ROLE_NAME_THAT_EXCEEDS_NORMAL_LENGTH_LIMITS_FOR_TESTING_PURPOSES"
    api: api/contract-review/settings/review-role-catogory-list.yml
    validate:
        - eq: ["status_code", 400]
