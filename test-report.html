
<!DOCTYPE html>
<html>
<head>
  <meta charset="utf-8">
  <title>TestCafe Report</title>
  <link rel="stylesheet" href="https://unpkg.com/element-ui/lib/theme-chalk/index.css">
  <script src="https://unpkg.com/vue@2.6.14/dist/vue.js"></script>
  <script src="https://unpkg.com/element-ui/lib/index.js"></script>
  <style>
      .report-container { padding: 20px; max-width: 1200px; margin: 0 auto; }
      .stat-card { margin-bottom: 20px; box-shadow: 0 2px 12px rgba(0,0,0,0.1); }
      .screenshot-thumb { width: 60px; cursor: pointer; }
  </style>
</head>
<body>
  <div id="app" class="report-container">
      <el-card class="stat-card">
          <h1>测试报告概览</h1>
          <el-row :gutter="20">
              <el-col :span="6">
                  <div class="grid-content">
                      <el-statistic title="总用例数" :value="2"></el-statistic>
                  </div>
              </el-col>
              <!-- 其他统计项 -->
          </el-row>
          <p>开始时间: 2025-03-05 18:14:22</p>
          <p>浏览器: Chrome ********* / Windows 10</p>
      </el-card>

      <el-table :data="testCases" stripe style="width: 100%">
          <el-table-column prop="id" label="ID" width="80"></el-table-column>
          <el-table-column prop="name" label="测试用例"></el-table-column>
          <el-table-column label="状态" width="120">
              <template slot-scope="{row}">
                  <el-tag :type="statusType(row.status)" size="medium">
                      {{ row.status.toUpperCase() }}
                  </el-tag>
              </template>
          </el-table-column>
          <el-table-column label="截图" width="100">
              <template slot-scope="{row}">
                  <img v-if="row.screenshot" class="screenshot-thumb" 
                       :src="row.screenshot" @click="showScreenshot(row.screenshot)">
              </template>
          </el-table-column>
      </el-table>

      <el-dialog :visible.sync="dialogVisible" width="70%">
          <img :src="currentScreenshot" style="width: 100%">
      </el-dialog>
  </div>

  <script>
  new Vue({
      el: '#app',
      data: () => ({
          testCases: [],
          dialogVisible: false,
          currentScreenshot: ''
      }),
      methods: {
          statusType(status) {
              return { passed: 'success', failed: 'danger', skipped: 'warning' }[status] || 'info'
          },
          showScreenshot(url) {
              this.currentScreenshot = url;
              this.dialogVisible = true;
          }
      }
  })
  </script>
</body>
</html>