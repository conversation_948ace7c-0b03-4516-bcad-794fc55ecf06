const { genFile } = require('../genFile')

const genStr = (obj) => {
  return `function setStorageAfterLoad(storageData) {
    function setCookie(name, value, options) {
      var cookieString = name + "=" + value + "; path=/";
      if (options.path) cookieString += "; path=" + options.path;
      if (options.domain) cookieString += "; domain=" + options.domain;
      if (options.expires) cookieString += "; expires=" + options.expires;
      document.cookie = cookieString;
    }

    function setLocalStorage(items) {
      items.forEach(function(item) {
        localStorage.setItem(item.key, item.value);
      });
    }

    function setSessionStorage(items) {
      items.forEach(function(item) {
        sessionStorage.setItem(item.key, item.value);
      });
    }

    function applyStorageSettings() {
      // Set cookies
      storageData.cookies.forEach(function(cookie) {
        setCookie(cookie.name, cookie.value, {
          path: cookie.path,
          domain: cookie.domain,
          expires: cookie.expires
        });
      });

      // Set localStorage items
      if (storageData.localStorage) {
        setLocalStorage(storageData.localStorage);
      }

      // Set sessionStorage items
      if (storageData.sessionStorage) {
        setSessionStorage(storageData.sessionStorage);
      }
    }

    // Check if the page has already loaded
    if (document.readyState === "complete") {
      applyStorageSettings();
    } else {
      window.addEventListener("load", applyStorageSettings);
    }
  }
  setStorageAfterLoad(${JSON.stringify(obj)});`
}

const genInjectFile = (xmindInfo, filePath) => {
  const mock = xmindInfo.config.mock || {}
  const str = genStr(mock)
  genFile(filePath, str)
}

exports.genInjectFile = genInjectFile