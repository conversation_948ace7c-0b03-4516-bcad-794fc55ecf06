import { Injectable } from '@nestjs/common';
import * as path from 'path';
import * as fs from 'fs';
import axios from 'axios';
const testService = require('../core/index.js');
const xmindparser = require('../core/xmindparserlib');

interface Task {
  caseName: string;
  browser: string;
  groupName: string;
  notifyUrl: string;
}

@Injectable()
export class AppService {
  // 使用单个运行任务数组维护执行状态
  private runningTasks: Task[] = [];
  private pendingQueue: Task[] = [];
  private readonly MAX_CONCURRENT = 2; // 最大并发数定义为常量

  addTask(task: Task): any {
    if ([this.pendingQueue, this.runningTasks].some(queue =>
      queue.some(t => t.caseName === task.caseName)
    )) {
      return {
        status: 'error',
        msg: `用例 ${task.caseName} 已在队列中或执行中`
      };
    }

    // 记录添加时的队列长度（添加前）
    const initialQueueLength = this.pendingQueue.length+this.runningTasks.length;
      
    this.pendingQueue.push(task);
    
    // 计算队列位置（从1开始）
    const queuePosition = initialQueueLength + 1;
    const queueTotal = this.pendingQueue.length;

   this.processQueue();

    return {
      status: 'success',
      msg: `用例 ${task.caseName} 已加入队列，当前处于队列第${initialQueueLength+1}位,${initialQueueLength===0?'正在':'稍后'}执行,目前队列总长度${queueTotal}`
    };
  }


  private processQueue(): void {
    while (this.runningTasks.length < this.MAX_CONCURRENT
      && this.pendingQueue.length > 0) {
      this.processNextTask();
    }
  }

  private processNextTask(): void {
    const task = this.pendingQueue.shift()!;
    this.runningTasks.push(task);
    this.sendMsg(task,`### 【${task.caseName}】UI自动测试开始唤起执行，请耐心等待`);

    this.executeTask(task)
      .then(result => this.handleTaskSuccess(task, result))
      .catch(error => this.handleTaskError(task, error))
      .finally(() => this.finalizeTaskProcessing(task));
  }

  private async executeTask(task: Task): Promise<any> {
    // 实际执行逻辑（保持原有逻辑）
    return testService.start(
      task.caseName,
      task.browser,
      task.groupName,
      (reportData) => this.sendResult(reportData, task.notifyUrl)
    );
  }
  sendMsg = async (task,info) => {
    axios.post(
      task.notifyUrl,
      JSON.stringify({
        msgtype: 'markdown',
        markdown: {
          title: '测试结果',
          text:  info
        }
      }),
      {
        headers: {
          'Content-Type': 'application/json',
        },
        httpsAgent: null,
        httpAgent: null,
        proxy: false,
      },
    );
  }
  async stopTask(caseName: string): Promise<void> {
    try {
      await testService.stop(caseName);
    } catch (error) {
      console.error(`停止任务 ${caseName} 失败:`, error.message);
    } finally {
      // 无论停止是否成功，都从运行队列移除
      this.runningTasks = this.runningTasks.filter(t => t.caseName !== caseName);
      this.processQueue(); // 触发后续任务处理
    }
  }


  private handleTaskSuccess(task: Task, result: any): void {
    console.log(`用例 ${task.caseName} 执行成功`, result);
  }

  private handleTaskError(task: Task, error: Error): void {
    console.error(`用例 ${task.caseName} 执行失败:`, error.message);
  }

  private finalizeTaskProcessing(task: Task): void {
    // 使用过滤方式避免引用问题
    this.runningTasks = this.runningTasks.filter(t => t.caseName !== task.caseName);
    this.processQueue(); // 触发后续任务处理
  }
  sendResult = async (reportData, url) => {
    try {
      const notification = {
        timestamp: new Date().toISOString(),
        port: reportData.port,
        caseName: reportData.caseName,
        startTime: reportData.startTime,
        testInfo: JSON.stringify(reportData.caseList),
        browser: reportData.browser,
        duration: (Date.now() - new Date(reportData.startTime).getTime()),
        totalCases: reportData.totalCases,
        isSuccess: reportData.isSuccess,
        userAgent: reportData.userAgent,
        fileTag: reportData.fileTag,
        groupName: reportData.groupName,
        env: process.env.NODE_ENV,
        notifyUrl: url || `https://oapi.dingtalk.com/robot/send?access_token=835b031db8556edc0210721e0d2012051439831a3f9b56fe3e0243b71baedc60`,
        apiBaseUrl: process.env.API_SERVER_URL,
      }
      console.log('[Success] Test finish notification:', notification);
      const notifyUrl = `${process.env.API_SERVER_URL}/notify/finishTest`;
      await axios.post(notifyUrl, notification, {
        headers: {
          'Content-Type': 'application/json',
          'X-Request-Source': 'testcafe-runner'
        }
      });
    } catch (notificationError) {
      console.error('[Critical] Failed to send notification:', notificationError.message);
    }
  }
  async generateXmind(jsonData: object): Promise<any> {
    console.log('jsonData:', jsonData);
    const reportPath = path.join(__dirname, '../core/temp')
    console.log('reportPath:', reportPath);


    // 在文件生成后清除定时器
    let parser = new xmindparser()
    const result = await parser.JSONToXmind(jsonData, reportPath);
    const regex = /[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}/;

    const match = result.match(regex);

    if (match) {
      console.log("Extracted UUID:", match[0]);
      return match[0];
    } else {
      console.log("No UUID found.");
      return null;
    }
  }
}
