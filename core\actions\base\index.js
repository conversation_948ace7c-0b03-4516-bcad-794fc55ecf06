const { genIgnoreError, genOptions } = require('../utils.js');

const click = (selector, options, ignore) => {
  return genIgnoreError(`await t.click(await setCurrentElement(${selector}), ${genOptions(options)})`, ignore)
}

const rightClick = (selector, ignore) => {
  return genIgnoreError(`await t.rightClick(await setCurrentElement(${selector}))`, ignore)
}

const doubleClick = (selector, ignore) => {
  return genIgnoreError(`await t.doubleClick(await setCurrentElement(${selector}))`, ignore)
}

const keypress = (value, ignore) => {
  return genIgnoreError(`await t.pressKey('${value}')`, ignore)
}

const input = (value, ignore) => {
  return genIgnoreError(`${keypress('ctrl+a')}\n${keypress('delete')}\nawait t.typeText(await getCurrentElement(), '${value}')`, ignore)
}

const choose = (value, ignore) => {
  const [text, target, className] = value.split('.');
  if (target) {
    return genIgnoreError(`await t.click(await setCurrentElement('${target}', true, '${text}'))`, ignore)
  }
  return genIgnoreError(`await t.click(await setCurrentElement('.${className}', true, '${text}'))`, ignore)
}

const hover = (selector, ignore) => {
  return genIgnoreError(`await t.hover(await setCurrentElement(${selector}))`, ignore)
}

const wait = (value) => {
  return `await t.wait(${value})`
}

const debug = () => {
  return `await t.debug()`
}

const refresh = () => {
  return `await t.eval(() => location.reload(true));`
}

const resize = (value) => {
  const { width, height } = value
  return `await t.resizeWindow(${width}, ${height})`
}

const drag = (step) => {

}

exports.click = click
exports.rightClick = rightClick
exports.doubleClick = doubleClick
exports.input = input
exports.keypress = keypress
exports.choose = choose
exports.hover = hover
exports.wait = wait
exports.debug = debug
exports.refresh = refresh
exports.resize = resize
