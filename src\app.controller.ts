import { Controller, Post, Body, BadRequestException, Get, Query, Res, StreamableFile } from '@nestjs/common';
import { AppService } from './app.service';
import { Response } from 'express';
import * as path from 'path';
import * as fs from 'fs';

@Controller()
export class AppController {
  constructor(private readonly appService: AppService) { }

  @Post('/startTest')
  async startTest(@Body('caseName') caseName: string, @Body('browser') browser: string = 'chrome:headless', @Body('notifyUrl') notifyUrl: string,@Body('groupName') groupName: string ) {
    if (!caseName) {
      throw new BadRequestException('caseName is required');
    }
    const data = {
      caseName, browser, notifyUrl,groupName
    }
    const result = await this.appService.addTask(data);
    return {
      message: 200,
      data: result
    };
  }
  @Post('/stopTest')
  async stopTest(@Body('caseName') caseName: string) {
    if (!caseName) {
      throw new BadRequestException('caseName is required');
    }
    const result = await this.appService.stopTask(caseName);
    return {
      message: 200,
      data: result
    };
  }
  @Post('/generate')
  async generateXmind(@Body() body: any) {
    try {
      const uuid = await this.appService.generateXmind(body);
      return {
        code: 0,
        data: `${process.env.API_SERVER_URL}/download?uuid=${uuid}`
      }
    } catch (error) {
      console.log(error)
    }
  }
  @Get('/download')
  async download(@Query('uuid') uuid: any, @Res({ passthrough: true }) res: Response) {
    const filePath = path.join(__dirname, '../core/temp', uuid + '.xmind');
    try {
      const filename = path.basename(filePath);
      res.set({
        'Content-Type': 'application/octet-stream',
        'Content-Disposition': `attachment; filename="${uuid}.xmind"`
      });

      const fileStream = fs.createReadStream(filePath);
      return new StreamableFile(fileStream);
    } catch (error) {
      console.log(error)
    }
  }
}