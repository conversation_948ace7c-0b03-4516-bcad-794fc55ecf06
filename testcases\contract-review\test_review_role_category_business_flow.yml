- config:
    name: 审查角色合同类型列表业务流程测试
    base_url: ${ENV(base_url)}
    variables:
        - app_Id: ${ENV(app_Id)}

- test:
    name: 业务流程-获取审查角色合同类型列表并验证数据完整性
    variables:
        - params: "reviewRole=LEGAL_REVIEWER"
    api: api/contract-review/settings/review-role-catogory-list.yml
    extract:
        - categoryList: content.categoryList
        - firstCategory: content.categoryList.0.category
        - firstCategoryName: content.categoryList.0.categoryName
        - firstInventoryId: content.categoryList.0.inventoryId
        - firstInventoryStatus: content.categoryList.0.inventoryStatus
    validate:
        - eq: ["status_code", 200]
        - type_match: ["content.categoryList", list]
        - length_greater_than: ["content.categoryList", 0]
        - type_match: ["content.categoryList.0.category", str]
        - type_match: ["content.categoryList.0.categoryName", str]
        - type_match: ["content.categoryList.0.inventoryStatus", str]
        - length_greater_than: ["content.categoryList.0.category", 0]
        - length_greater_than: ["content.categoryList.0.categoryName", 0]

- test:
    name: 业务流程-验证不同审查角色返回不同合同类型
    variables:
        - params: "reviewRole=HR_REVIEWER"
    api: api/contract-review/settings/review-role-catogory-list.yml
    extract:
        - hrCategoryList: content.categoryList
    validate:
        - eq: ["status_code", 200]
        - type_match: ["content.categoryList", list]

- test:
    name: 业务流程-验证审查清单状态字段
    variables:
        - params: "reviewRole=LEGAL_REVIEWER"
    api: api/contract-review/settings/review-role-catogory-list.yml
    validate:
        - eq: ["status_code", 200]
        - contains: ["content.categoryList.0.inventoryStatus", "ENABLED", "DISABLED"]

- test:
    name: 业务流程-验证inventoryId字段逻辑
    variables:
        - params: "reviewRole=LEGAL_REVIEWER"
    api: api/contract-review/settings/review-role-catogory-list.yml
    validate:
        - eq: ["status_code", 200]
        - type_match: ["content.categoryList", list]
        # inventoryId可能为空字符串或有值，都是合法的
