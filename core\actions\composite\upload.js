const http = require('http');
const https = require('https');
const path = require('path');
const fs = require('fs');
const { setCurrentElement } = require('../../currentElement/index')

const getUploadFilePath = async (value) => {
  let downloadSuccess = false;
  let useRemote = false;

  if (value.startsWith('https://')) {
    useRemote = 'https';
  } else if (value.startsWith('http://')) {
    useRemote = 'http';
  }

  if (useRemote) {
    console.log(`----- 正在从 ${value} 下载文件 -----`);
    const protocol = useRemote === 'http' ? http : https;
    const temp = value.split('.');
    const suffix = temp[temp.length - 1];
    const name = `${Date.now()}.${suffix}`;
    downloadSuccess = await new Promise(resolve => {
      protocol
        .get(value, res => {
          const dir = path.resolve(process.cwd(), './tmp');
          if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
          }
          const filepath = path.resolve(process.cwd(), `./tmp/${name}`);
          const file = fs.createWriteStream(filepath);
          res.pipe(file);
          file.on('finish', () => {
            file.close();
            console.log('----- 文件下载完成 -----');
            resolve(filepath);
          });
          file.on('error', resolve);
        })
        .on('error', resolve);
    });

    if (!downloadSuccess) throw new Error('文件下载失败');

    return [ downloadSuccess ]
  }
  return [ path.resolve(process.cwd(), value ? value : './file/default/testFile.pdf') ]
}

const upload = (selector, variableName) => {
  return `await t.setFilesToUpload(${selector}, ${variableName})\n`
};

exports.upload = upload
exports.getUploadFilePath = getUploadFilePath