const { click, rightClick, doubleClick, input, keypress, choose, hover, wait, debug, refresh, resize } = require('./base/index')
const { assert, assertStyle, assertAttr } = require('./assert/index')
const { composite } = require('./composite/index')
const { genIgnoreError } = require('./utils')

const actions = {
  base: {
    click,
    rightClick,
    doubleClick,
    input,
    keypress,
    choose,
    hover,
    wait,
    debug,
    refresh,
    resize,
  },
  assert: {
    base: assert,
    style: assertStyle,
    attr: assertAttr,
  },
  composite,
  utils: {
    genIgnoreError
  },
}

module.exports = actions