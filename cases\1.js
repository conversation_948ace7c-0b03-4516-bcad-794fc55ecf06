
    const { Selector, ClientFunction } = require('testcafe');
    const axios = require('axios');
    const fs = require('fs');
    const path = require('path');
    const { createHash } = require('crypto');
    const fileType = require('file-type');
    const tempDirPath = "/Users/<USER>/Documents/work/yunxia/public-ui-test/tmp"
    const compareFileDirPath = "/Users/<USER>/Documents/work/yunxia/public-ui-test/compare/1"
    
  const getMD5 = fd => {
    return new Promise((resolve, reject) => {
      const readStream = fd.createReadStream()
      const md5Hash = createHash('md5')
      readStream.on('data', (chunk) => {
        md5Hash.update(chunk)
      })
  
      readStream.on('end', () => {
        const contentMd5HashValue = md5Hash.digest('base64')
        resolve(contentMd5HashValue)
      })
  
      readStream.on('error', (error) => {
        console.error('计算 Content-MD5 时出错：', error)
        reject(error)
      })
    })
  }

  const genRequestData = (obj) => {
    return {
      ip: ['*************', '**************'],
      accountId: '',
      serviceId: 'e35f3884-cef4-4bc1-81a3-540bdae87a28',
      serviceType: 'zsy',
      internal: true,
      expire: 7200000,
      callback: 'http://************:8086/evi-service/evidence/v1/uploaded/presevation/notify',
      queue: 'file_system_callback_test1',
      convertToHTML: true,
      convertToPDF: false,
      projectId: 'UI-FE',
      ...obj,
    }
  }

  const upload = async (file) => {
    const { mime: contentType } = await fileType.fromFile(file)
    const fd = await fs.promises.open(file)
    const { size: fileSize } = await fd.stat()
    const contentMd5 = await getMD5(fd)
    const fileName = path.basename(file)
    const fileStream = fs.createReadStream(file)

    const data = genRequestData({ contentType, contentMd5, fileSize, fileName })

    const { data: { url, fileKey } } = await axios({
      method: 'POST',
      url: 'http://file-system.testk8s.tsign.cn/file-system/fileService/getSignUrl',
      data,
      headers: {
        'Content-Type': 'application/json',
        'X-timevale-project-id': '1111563774',
      },
    })

    await axios({
      method: 'PUT',
      url,
      data: fileStream,
      headers: {
        'Content-Type': contentType,
      },
    })

    return fileKey
  }

const compare = async (t, selector, file) => {
  const imageFileName = Date.now() + '.png'
  const screenShotPath = path.resolve(tempDirPath, imageFileName)
  await t.takeElementScreenshot(selector, imageFileName)

  const fileKey1 = await upload(screenShotPath)
  // 判断比对文件所在的文件夹是否存在
  if (!fs.existsSync(compareFileDirPath)) {
    console.log('比对文件夹不存在 创建文件夹:', compareFileDirPath)
    fs.mkdirSync(compareFileDirPath)
  }

  // 判断比对文件是否存在
  if (!fs.existsSync(path.resolve(compareFileDirPath, file))) {
    console.log('未找到比对图片 新增比对图片:', compareFileDirPath)
    fs.copyFileSync(screenShotPath, path.resolve(compareFileDirPath, file))
  }

  const fileKey2 = await upload(path.resolve(compareFileDirPath, file))

  const { data } = await axios({
    method: 'POST',
    url: 'http://file-microscope.testk8s.tsign.cn/compareFile',
    data: {
      env: 'test',
      fileKey1,
      fileKey2,
      compareType: 2,
      compareLevel: 1,
      taskName: '前端UI自动化测试用',
      sourceTypeCode: 'EPAAS_DOC_TEMPLATE_UI_AUTOTEST',
    },
  })

  const similarityList = JSON.parse(data.result.similarityList)

  if (similarityList[0] < 0.99) throw new Error('阈值 0.99, 比对值 ' + similarityList[0] + ', 比对不通过')
}

    
    fixture `1`
      .page `https://testinapp.tsign.cn/esa-dev-web/cebadddf-23fd-4e5c-9dcb-850689ba8ae6`
      .skipJsErrors();

      test('新增必填校验不通过并取消', async t => {
      
      await t.wait(100);
      let elementSelector = ''
      let assertElementSelector = ''
      await t.wait(3000)
 elementSelector = Selector('[name="username"]').filterVisible()
 await t.click(elementSelector)
 await t.typeText(elementSelector, 'yunxia')
 elementSelector = Selector('[name="password"]').filterVisible()
 await t.click(elementSelector)
 await t.typeText(elementSelector, 'SKy930629')
 elementSelector = Selector('[name="submit"]').filterVisible()
 await t.click(elementSelector)
await t.wait(3000)
       elementSelector = Selector('[data-esign-inject-name="table-head-add-button"]').filterVisible()
 await t.click(elementSelector)
 elementSelector = Selector('[data-esign-inject-name="表单项prop1"]').filterVisible()
 await t.click(elementSelector)
 await t.typeText(elementSelector, '1')
 elementSelector = Selector('[data-esign-inject-name="表单项prop2"]').filterVisible()
 await t.click(elementSelector)
 await t.typeText(elementSelector, '2')
 elementSelector = Selector('[data-esign-inject-name="表单项prop3"]').filterVisible()
 await t.click(elementSelector)
 await t.typeText(elementSelector, '3')
 elementSelector = Selector('[data-esign-inject-name="addDialogCancel"]').filterVisible()
 await t.click(elementSelector)
await t.wait(1000)
await compare(t, Selector("body"), '未新增.png')
    });


test('新增异常校验不通过并关闭', async t => {
      
      await t.wait(100);
      let elementSelector = ''
      let assertElementSelector = ''
      await t.wait(3000)
 elementSelector = Selector('[name="username"]').filterVisible()
 await t.click(elementSelector)
 await t.typeText(elementSelector, 'yunxia')
 elementSelector = Selector('[name="password"]').filterVisible()
 await t.click(elementSelector)
 await t.typeText(elementSelector, 'SKy930629')
 elementSelector = Selector('[name="submit"]').filterVisible()
 await t.click(elementSelector)
await t.wait(3000)
       elementSelector = Selector('[data-esign-inject-name="table-head-add-button"]').filterVisible()
 await t.click(elementSelector)
 elementSelector = Selector('[data-esign-inject-name="表单项prop1"]').filterVisible()
 await t.click(elementSelector)
 await t.typeText(elementSelector, '1')
 elementSelector = Selector('[data-esign-inject-name="表单项prop2"]').filterVisible()
 await t.click(elementSelector)
 await t.typeText(elementSelector, '2')
 elementSelector = Selector('[data-esign-inject-name="表单项prop3"]').filterVisible()
 await t.click(elementSelector)
 await t.typeText(elementSelector, '3')
 elementSelector = Selector('.es-dialog__header .es-dialog__header-button').filterVisible()
 await t.click(elementSelector)
await t.wait(1000)
await compare(t, Selector("body"), '未新增.png')
    });


test('新增必填校验不通过并点击确定', async t => {
      
      await t.wait(100);
      let elementSelector = ''
      let assertElementSelector = ''
      await t.wait(3000)
 elementSelector = Selector('[name="username"]').filterVisible()
 await t.click(elementSelector)
 await t.typeText(elementSelector, 'yunxia')
 elementSelector = Selector('[name="password"]').filterVisible()
 await t.click(elementSelector)
 await t.typeText(elementSelector, 'SKy930629')
 elementSelector = Selector('[name="submit"]').filterVisible()
 await t.click(elementSelector)
await t.wait(3000)
       elementSelector = Selector('[data-esign-inject-name="table-head-add-button"]').filterVisible()
 await t.click(elementSelector)
 elementSelector = Selector('[data-esign-inject-name="表单项prop1"]').filterVisible()
 await t.click(elementSelector)
 await t.typeText(elementSelector, '1')
 elementSelector = Selector('[data-esign-inject-name="表单项prop2"]').filterVisible()
 await t.click(elementSelector)
 await t.typeText(elementSelector, '2')
 elementSelector = Selector('[data-esign-inject-name="表单项prop3"]').filterVisible()
 await t.click(elementSelector)
 await t.typeText(elementSelector, '3')
await t.click(await Selector('button').withText('确定'))
await t.wait(1000)
 assertElementSelector = Selector('.es-message')
 await t.expect((await assertElementSelector())['value'] || (await assertElementSelector())['innerText'].replace(/[\r\n]+/g, ''))['contains']('参数为空', 'fail')
await compare(t, Selector("body"), '未新增.png')
    });


test('新增异常校验不通过并点击确定', async t => {
      
      await t.wait(100);
      let elementSelector = ''
      let assertElementSelector = ''
      await t.wait(3000)
 elementSelector = Selector('[name="username"]').filterVisible()
 await t.click(elementSelector)
 await t.typeText(elementSelector, 'yunxia')
 elementSelector = Selector('[name="password"]').filterVisible()
 await t.click(elementSelector)
 await t.typeText(elementSelector, 'SKy930629')
 elementSelector = Selector('[name="submit"]').filterVisible()
 await t.click(elementSelector)
await t.wait(3000)
       elementSelector = Selector('[data-esign-inject-name="table-head-add-button"]').filterVisible()
 await t.click(elementSelector)
 elementSelector = Selector('[data-esign-inject-name="表单项prop1"]').filterVisible()
 await t.click(elementSelector)
 await t.typeText(elementSelector, '1')
 elementSelector = Selector('[data-esign-inject-name="表单项prop2"]').filterVisible()
 await t.click(elementSelector)
 await t.typeText(elementSelector, '2')
 elementSelector = Selector('[data-esign-inject-name="表单项prop3"]').filterVisible()
 await t.click(elementSelector)
 await t.typeText(elementSelector, '3')
await t.click(await Selector('button').withText('确定'))
await t.wait(1000)
 assertElementSelector = Selector('.es-message')
 await t.expect((await assertElementSelector())['value'] || (await assertElementSelector())['innerText'].replace(/[\r\n]+/g, ''))['contains']('参数为空', 'fail')
await compare(t, Selector("body"), '未新增.png')
    });


test('新增校验通过并点击确定', async t => {
      
      await t.wait(100);
      let elementSelector = ''
      let assertElementSelector = ''
      await t.wait(3000)
 elementSelector = Selector('[name="username"]').filterVisible()
 await t.click(elementSelector)
 await t.typeText(elementSelector, 'yunxia')
 elementSelector = Selector('[name="password"]').filterVisible()
 await t.click(elementSelector)
 await t.typeText(elementSelector, 'SKy930629')
 elementSelector = Selector('[name="submit"]').filterVisible()
 await t.click(elementSelector)
await t.wait(3000)
       elementSelector = Selector('[data-esign-inject-name="table-head-add-button"]').filterVisible()
 await t.click(elementSelector)
 elementSelector = Selector('[data-esign-inject-name="表单项prop1"]').filterVisible()
 await t.click(elementSelector)
 await t.typeText(elementSelector, '1')
 elementSelector = Selector('[data-esign-inject-name="表单项prop2"]').filterVisible()
 await t.click(elementSelector)
 await t.typeText(elementSelector, '2')
 elementSelector = Selector('[data-esign-inject-name="表单项prop3"]').filterVisible()
 await t.click(elementSelector)
 await t.typeText(elementSelector, '3')
 elementSelector = Selector('[data-esign-inject-name="表单项name"]').filterVisible()
 await t.click(elementSelector)
 await t.typeText(elementSelector, '呃呃呃呃呃')
await t.click(await Selector('button').withText('确定'))
await t.wait(2000)
await compare(t, Selector("body"), '新增.png')
    });


test('查看数据', async t => {
      
      await t.wait(100);
      let elementSelector = ''
      let assertElementSelector = ''
      await t.wait(3000)
 elementSelector = Selector('[name="username"]').filterVisible()
 await t.click(elementSelector)
 await t.typeText(elementSelector, 'yunxia')
 elementSelector = Selector('[name="password"]').filterVisible()
 await t.click(elementSelector)
 await t.typeText(elementSelector, 'SKy930629')
 elementSelector = Selector('[name="submit"]').filterVisible()
 await t.click(elementSelector)
await t.wait(3000)
       elementSelector = Selector('[data-esign-inject-name="按钮详情"]').filterVisible()
 await t.click(elementSelector)
 assertElementSelector = Selector('[data-esign-inject-name="表单项name"]')
 await t.expect((await assertElementSelector())['value'] || (await assertElementSelector())['innerText'].replace(/[\r\n]+/g, ''))['eql']('呃呃呃呃呃', 'fail')
 assertElementSelector = Selector('[data-esign-inject-name="表单项name"]')
 const attr2 = await assertElementSelector.getAttribute('disabled');
await t.expect(attr2).eql('disabled')
 assertElementSelector = Selector('[data-esign-inject-name="表单项prop1"]')
 await t.expect((await assertElementSelector())['value'] || (await assertElementSelector())['innerText'].replace(/[\r\n]+/g, ''))['eql']('1', 'fail')
 assertElementSelector = Selector('[data-esign-inject-name="表单项prop1"]')
 const attr4 = await assertElementSelector.getAttribute('disabled');
await t.expect(attr4).eql('disabled')
 assertElementSelector = Selector('[data-esign-inject-name="表单项prop2"]')
 await t.expect((await assertElementSelector())['value'] || (await assertElementSelector())['innerText'].replace(/[\r\n]+/g, ''))['eql']('2', 'fail')
 assertElementSelector = Selector('[data-esign-inject-name="表单项prop2"]')
 const attr6 = await assertElementSelector.getAttribute('disabled');
await t.expect(attr6).eql('disabled')
 assertElementSelector = Selector('[data-esign-inject-name="表单项prop3"]')
 await t.expect((await assertElementSelector())['value'] || (await assertElementSelector())['innerText'].replace(/[\r\n]+/g, ''))['eql']('3', 'fail')
 assertElementSelector = Selector('[data-esign-inject-name="表单项prop3"]')
 const attr8 = await assertElementSelector.getAttribute('disabled');
await t.expect(attr8).eql('disabled')
 elementSelector = Selector('.es-dialog__header .es-dialog__header-button').filterVisible()
 await t.click(elementSelector)
await compare(t, Selector("body"), '新增.png')
    });


test('搜索有内容', async t => {
      
      await t.wait(100);
      let elementSelector = ''
      let assertElementSelector = ''
      await t.wait(3000)
 elementSelector = Selector('[name="username"]').filterVisible()
 await t.click(elementSelector)
 await t.typeText(elementSelector, 'yunxia')
 elementSelector = Selector('[name="password"]').filterVisible()
 await t.click(elementSelector)
 await t.typeText(elementSelector, 'SKy930629')
 elementSelector = Selector('[name="submit"]').filterVisible()
 await t.click(elementSelector)
await t.wait(3000)
       elementSelector = Selector('.filter-item:nth-child(1) .filter-item-context').filterVisible()
 await t.click(elementSelector)
 await t.typeText(elementSelector, '呃呃呃呃呃')
 elementSelector = Selector('.filter-item:nth-child(2) .filter-item-context input').filterVisible()
 await t.click(elementSelector)
 await t.typeText(elementSelector, '1')
 elementSelector = Selector('[data-esign-inject-name="按钮查 询"]').filterVisible()
 await t.click(elementSelector)
await t.wait(1000)
 assertElementSelector = Selector('.es-table__empty-text')
 await t.expect(assertElementSelector.exists)['notOk']()
 assertElementSelector = Selector('.filter-item:nth-child(1) .filter-item-context')
 await t.expect((await assertElementSelector())['value'] || (await assertElementSelector())['innerText'].replace(/[\r\n]+/g, ''))['eql']('呃呃呃呃呃', 'fail')
 assertElementSelector = Selector('.filter-item:nth-child(2) .filter-item-context input')
 await t.expect((await assertElementSelector())['value'] || (await assertElementSelector())['innerText'].replace(/[\r\n]+/g, ''))['eql']('1', 'fail')
    });


test('搜索无内容并重置', async t => {
      
      await t.wait(100);
      let elementSelector = ''
      let assertElementSelector = ''
      await t.wait(3000)
 elementSelector = Selector('[name="username"]').filterVisible()
 await t.click(elementSelector)
 await t.typeText(elementSelector, 'yunxia')
 elementSelector = Selector('[name="password"]').filterVisible()
 await t.click(elementSelector)
 await t.typeText(elementSelector, 'SKy930629')
 elementSelector = Selector('[name="submit"]').filterVisible()
 await t.click(elementSelector)
await t.wait(3000)
       elementSelector = Selector('.filter-item:nth-child(1) .filter-item-context').filterVisible()
 await t.click(elementSelector)
 await t.typeText(elementSelector, '好吧')
 elementSelector = Selector('.filter-item:nth-child(2) .filter-item-context input').filterVisible()
 await t.click(elementSelector)
 await t.typeText(elementSelector, '1')
 elementSelector = Selector('[data-esign-inject-name="按钮查 询"]').filterVisible()
 await t.click(elementSelector)
await t.wait(1000)
 assertElementSelector = Selector('.es-table__empty-text')
 await t.expect(assertElementSelector.exists)['ok']()
 elementSelector = Selector('[data-esign-inject-name="按钮重 置"]').filterVisible()
 await t.click(elementSelector)
await t.wait(1000)
 assertElementSelector = Selector('.es-table__empty-text')
 await t.expect(assertElementSelector.exists)['notOk']()
    });


test('更多存在', async t => {
      
      await t.wait(100);
      let elementSelector = ''
      let assertElementSelector = ''
      await t.wait(3000)
 elementSelector = Selector('[name="username"]').filterVisible()
 await t.click(elementSelector)
 await t.typeText(elementSelector, 'yunxia')
 elementSelector = Selector('[name="password"]').filterVisible()
 await t.click(elementSelector)
 await t.typeText(elementSelector, 'SKy930629')
 elementSelector = Selector('[name="submit"]').filterVisible()
 await t.click(elementSelector)
await t.wait(3000)
       assertElementSelector = Selector('.filter-btn .expand-collapse')
 await t.expect(assertElementSelector.exists)['ok']()
 assertElementSelector = Selector('.filter-item:nth-child(4)')
 const style1 = await assertElementSelector.style
await t.expect(style1['display']).eql('none')
 elementSelector = Selector('.filter-btn .expand-collapse').filterVisible()
 await t.click(elementSelector)
 assertElementSelector = Selector('.filter-item:nth-child(4)')
 const style3 = await assertElementSelector.style
await t.expect(style3['display']).notEql('none')
    });


test('编辑必填校验不通过并取消', async t => {
      
      await t.wait(100);
      let elementSelector = ''
      let assertElementSelector = ''
      await t.wait(3000)
 elementSelector = Selector('[name="username"]').filterVisible()
 await t.click(elementSelector)
 await t.typeText(elementSelector, 'yunxia')
 elementSelector = Selector('[name="password"]').filterVisible()
 await t.click(elementSelector)
 await t.typeText(elementSelector, 'SKy930629')
 elementSelector = Selector('[name="submit"]').filterVisible()
 await t.click(elementSelector)
await t.wait(3000)
       elementSelector = Selector('[data-esign-inject-name="按钮编辑"]').filterVisible()
 await t.click(elementSelector)
 elementSelector = Selector('[data-esign-inject-name="表单项name"]').filterVisible()
 await t.click(elementSelector)
await t.pressKey(' ctrl+a delete')
 elementSelector = Selector('[data-esign-inject-name="editDialogCancel"]').filterVisible()
 await t.click(elementSelector)
await t.wait(1000)
await compare(t, Selector("body"), '新增.png')
    });


test('编辑异常校验不通过并取消', async t => {
      
      await t.wait(100);
      let elementSelector = ''
      let assertElementSelector = ''
      await t.wait(3000)
 elementSelector = Selector('[name="username"]').filterVisible()
 await t.click(elementSelector)
 await t.typeText(elementSelector, 'yunxia')
 elementSelector = Selector('[name="password"]').filterVisible()
 await t.click(elementSelector)
 await t.typeText(elementSelector, 'SKy930629')
 elementSelector = Selector('[name="submit"]').filterVisible()
 await t.click(elementSelector)
await t.wait(3000)
       elementSelector = Selector('[data-esign-inject-name="按钮编辑"]').filterVisible()
 await t.click(elementSelector)
 elementSelector = Selector('[data-esign-inject-name="表单项name"]').filterVisible()
 await t.click(elementSelector)
await t.pressKey(' ctrl+a delete')
 elementSelector = Selector('.es-dialog__header .es-dialog__header-button').filterVisible()
 await t.click(elementSelector)
await t.wait(1000)
await compare(t, Selector("body"), '新增.png')
    });


test('编辑异常校验不通过并点击关闭', async t => {
      
      await t.wait(100);
      let elementSelector = ''
      let assertElementSelector = ''
      await t.wait(3000)
 elementSelector = Selector('[name="username"]').filterVisible()
 await t.click(elementSelector)
 await t.typeText(elementSelector, 'yunxia')
 elementSelector = Selector('[name="password"]').filterVisible()
 await t.click(elementSelector)
 await t.typeText(elementSelector, 'SKy930629')
 elementSelector = Selector('[name="submit"]').filterVisible()
 await t.click(elementSelector)
await t.wait(3000)
       elementSelector = Selector('[data-esign-inject-name="按钮编辑"]').filterVisible()
 await t.click(elementSelector)
 elementSelector = Selector('[data-esign-inject-name="表单项name"]').filterVisible()
 await t.click(elementSelector)
await t.pressKey(' ctrl+a delete')
 elementSelector = Selector('[data-esign-inject-name="editDialogConfirm"]').filterVisible()
 await t.click(elementSelector)
await t.wait(1000)
 assertElementSelector = Selector('.es-message')
 await t.expect((await assertElementSelector())['value'] || (await assertElementSelector())['innerText'].replace(/[\r\n]+/g, ''))['contains']('参数为空', 'fail')
await compare(t, Selector("body"), '新增.png')
    });


test('编辑异常校验不通过并点击关闭', async t => {
      
      await t.wait(100);
      let elementSelector = ''
      let assertElementSelector = ''
      await t.wait(3000)
 elementSelector = Selector('[name="username"]').filterVisible()
 await t.click(elementSelector)
 await t.typeText(elementSelector, 'yunxia')
 elementSelector = Selector('[name="password"]').filterVisible()
 await t.click(elementSelector)
 await t.typeText(elementSelector, 'SKy930629')
 elementSelector = Selector('[name="submit"]').filterVisible()
 await t.click(elementSelector)
await t.wait(3000)
       elementSelector = Selector('[data-esign-inject-name="按钮编辑"]').filterVisible()
 await t.click(elementSelector)
 elementSelector = Selector('[data-esign-inject-name="表单项name"]').filterVisible()
 await t.click(elementSelector)
await t.pressKey(' ctrl+a delete')
 elementSelector = Selector('[data-esign-inject-name="editDialogConfirm"]').filterVisible()
 await t.click(elementSelector)
await t.wait(1000)
 assertElementSelector = Selector('.es-message')
 await t.expect((await assertElementSelector())['value'] || (await assertElementSelector())['innerText'].replace(/[\r\n]+/g, ''))['contains']('参数为空', 'fail')
await compare(t, Selector("body"), '新增.png')
    });


test('编辑校验通过并点击确定', async t => {
      
      await t.wait(100);
      let elementSelector = ''
      let assertElementSelector = ''
      await t.wait(3000)
 elementSelector = Selector('[name="username"]').filterVisible()
 await t.click(elementSelector)
 await t.typeText(elementSelector, 'yunxia')
 elementSelector = Selector('[name="password"]').filterVisible()
 await t.click(elementSelector)
 await t.typeText(elementSelector, 'SKy930629')
 elementSelector = Selector('[name="submit"]').filterVisible()
 await t.click(elementSelector)
await t.wait(3000)
       elementSelector = Selector('[data-esign-inject-name="按钮编辑"]').filterVisible()
 await t.click(elementSelector)
 elementSelector = Selector('[data-esign-inject-name="表单项prop3"]').filterVisible()
 await t.click(elementSelector)
await t.pressKey(' ctrl+a delete')
 elementSelector = Selector('[data-esign-inject-name="editDialogConfirm"]').filterVisible()
 await t.click(elementSelector)
await t.wait(1000)
await compare(t, Selector("body"), '编辑后.png')
    });


test('删除数据点击关闭', async t => {
      
      await t.wait(100);
      let elementSelector = ''
      let assertElementSelector = ''
      await t.wait(3000)
 elementSelector = Selector('[name="username"]').filterVisible()
 await t.click(elementSelector)
 await t.typeText(elementSelector, 'yunxia')
 elementSelector = Selector('[name="password"]').filterVisible()
 await t.click(elementSelector)
 await t.typeText(elementSelector, 'SKy930629')
 elementSelector = Selector('[name="submit"]').filterVisible()
 await t.click(elementSelector)
await t.wait(3000)
       elementSelector = Selector('[data-esign-inject-name="按钮删除"]').filterVisible()
 await t.click(elementSelector)
 elementSelector = Selector('.es-message-box__header .es-message-box__close').filterVisible()
 await t.click(elementSelector)
await compare(t, Selector("body"), '编辑后.png')
    });


test('删除数据点击取消', async t => {
      
      await t.wait(100);
      let elementSelector = ''
      let assertElementSelector = ''
      await t.wait(3000)
 elementSelector = Selector('[name="username"]').filterVisible()
 await t.click(elementSelector)
 await t.typeText(elementSelector, 'yunxia')
 elementSelector = Selector('[name="password"]').filterVisible()
 await t.click(elementSelector)
 await t.typeText(elementSelector, 'SKy930629')
 elementSelector = Selector('[name="submit"]').filterVisible()
 await t.click(elementSelector)
await t.wait(3000)
       elementSelector = Selector('[data-esign-inject-name="按钮删除"]').filterVisible()
 await t.click(elementSelector)
 elementSelector = Selector('[data-esign-inject-name="按钮取消"]').filterVisible()
 await t.click(elementSelector)
await compare(t, Selector("body"), '编辑后.png')
    });


test('删除数据点击确定', async t => {
      
      await t.wait(100);
      let elementSelector = ''
      let assertElementSelector = ''
      await t.wait(3000)
 elementSelector = Selector('[name="username"]').filterVisible()
 await t.click(elementSelector)
 await t.typeText(elementSelector, 'yunxia')
 elementSelector = Selector('[name="password"]').filterVisible()
 await t.click(elementSelector)
 await t.typeText(elementSelector, 'SKy930629')
 elementSelector = Selector('[name="submit"]').filterVisible()
 await t.click(elementSelector)
await t.wait(3000)
       elementSelector = Selector('[data-esign-inject-name="按钮删除"]').filterVisible()
 await t.click(elementSelector)
 elementSelector = Selector('[data-esign-inject-name="按钮确定"]').filterVisible()
 await t.click(elementSelector)
await t.wait(5000)
await compare(t, Selector("body"), '新增.png')
    });
    