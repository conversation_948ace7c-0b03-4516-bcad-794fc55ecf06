:root {
    --primary-color: #409EFF;
    --success-color: #67C23A;
    --danger-color: #F56C6C;
    --warning-color: #E6A23C;
  }

  body {
    background-color: #bddaff;
    font-family: "Helvetica Neue", Helvetica, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", Arial, sans-serif;
  }
.stat-item {
padding: 8px 15px;
margin: 5px 0;
background: #f8f9fa;
border-radius: 6px;
display: flex;
align-items: center;
transition: background 0.3s;
}

.stat-item:hover {
background: #f1f3f5;
}

.stat-item i {
width: 24px;
color: #606266;
font-size: 16px;
}

.stat-label {
color: #909399;
margin-right: 8px;
min-width: 70px;
}
/* 新增时间列样式 */
.el-table{
    border-radius: 12px;
}
.el-table .time-cell {
font-family: monospace;
font-size: 13px;
color: #606266;
}

/* 响应式处理 */
@media (max-width: 768px) {
.el-table__body .time-cell {
font-size: 12px;
}
}
.stat-value {
color: #303133;
font-weight: 500;
}

.stat-extra {
color: #909399;
font-size: 12px;
margin-left: 8px;
}
  .report-container {
    padding: 30px;
    max-width: 1400px;
    margin: 0 auto;
  }

  .stat-card {
    margin-bottom: 30px;
    border-radius: 12px;
    box-shadow: 0 6px 18px rgba(0,0,0,0.06);
    transition: transform 0.3s;
  }

  .stat-card:hover {
    transform: translateY(-3px);
  }

  .stat-icon {
    font-size: 24px;
    margin-right: 10px;
    vertical-align: middle;
  }

  .screenshot-thumb {
    width: 80px;
    height: 50px;
    cursor: pointer;
    border-radius: 4px;
    object-fit: cover;
    transition: transform 0.2s;
  }

  .screenshot-thumb:hover {
    transform: scale(1.05);
  }

  .error-detail {
    background: #fff;
    padding: 10px;
    margin: 15px 20px;
    border-radius: 8px;
    border-left: 4px solid var(--danger-color);
    box-shadow: 0 3px 6px rgba(0,0,0,0.05);
  }

  .error-stack {
    background: #1e1e1e;
    color: #d4d4d4;
    padding: 15px;
    border-radius: 6px;
    font-family: "Consolas", monospace;
    font-size: 14px;
    line-height: 1.6;
    max-height: 400px;
    overflow: auto;
    margin: 15px;
  }

  .error-stack div {
    padding: 2px 0;
  }

  .error-stack div:hover {
    background: #2d2d2d;
  }

  .status-tag {
    font-weight: 500;
    letter-spacing: 0.5px;
  }

  .header-title {
    text-align: center;
    margin: 30px 0;
    color: #2c3e50;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .header-title i {
    font-size: 32px;
    margin-right: 15px;
    color: var(--primary-color);
  }

  .chart-container {
    margin: 30px 0;
    padding: 20px;
    background: white;
    border-radius: 8px;
  }