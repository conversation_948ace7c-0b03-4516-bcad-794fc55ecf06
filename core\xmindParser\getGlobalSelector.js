const { getText, getNode, getChildrenNode, isUndefined } = require('./utils')

const getGlobalSelector = root => {
  const selectorArr = getChildrenNode(getNode(root, '自动化标识'))
  const result = {}
  selectorArr.forEach((item) => {
    const key = getText(item)
    result[key] = []
    const values = getChildrenNode(item)
    values.forEach((valueItem) => {
      const value = getText(valueItem)
      let [type, val] = value.split('://')
      if (!isUndefined(val)) {
        result[key].push(value)
      } else {
        const resultArr = []
        const traverse = (resultArr, node) => {
          const text = getText(node)
          if (text) resultArr.push(text)
          const children = getChildrenNode(node)
          children.forEach((childNode) => {
            traverse(resultArr, childNode)
          })
        }
        traverse(resultArr, valueItem)
        const resultKeyStr = resultArr
          .map(text => `[data-esign-inject-name="${text}"]`)
          .join(' ')
        result[key].push(`css://${resultKeyStr}`)
      }
    })
  })
  return result
}

exports.getGlobalSelector = getGlobalSelector