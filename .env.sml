# 发布平台上部署的稳定环境，实际对接业务模拟环境
NODE_ENV=sml
API_SERVER_URL = http://*************:3001
REDIS_HOST=r-bp19582998986ee4.redis.rds.aliyuncs.com
REDIS_PORT=6379
REDIS_DB=4
REDIS_PASSWORD=Share123



ARMS_ENV=pre

# This was inserted by `prisma init`:
# Environment variables declared in this file are automatically made available to Prisma.
# See the documentation for more detail: https://pris.ly/d/prisma-schema#accessing-environment-variables-from-the-schema

# Prisma supports the native connection string format for PostgreSQL, MySQL, SQLite, SQL Server, MongoDB and CockroachDB.
# See the documentation for all the connection string options: https://pris.ly/d/connection-strings

# DATABASE_URL=mysql://esign_awacs:<EMAIL>:3306/esign_awacs

# DING_ROBOT=https://oapi.dingtalk.com/robot/send?access_token=91e860c98b502759e5b8620b428340bd49f4237a34709550efea3c7d992668ea
DING_ROBOT_PROD=https://oapi.dingtalk.com/robot/send?access_token=ac4fc00159fe192229f93114fa3f39f019248fe7ea2318257a0ce7b00ec5b437
DING_ROBOT=https://oapi.dingtalk.com/robot/send?access_token=05dbe01373d4d1f1c2fc6b8539a994dc6569f582f32d3cb1c9ea3c3fd36d69f3
DING_ROBOT_TEST=https://oapi.dingtalk.com/robot/send?access_token=ff587c564dea914c9e0a405faa8c611dd8a0504c815d6d1307d643d20b811580
DING_ROBOT_PRE=https://oapi.dingtalk.com/robot/send?access_token=ff587c564dea914c9e0a405faa8c611dd8a0504c815d6d1307d643d20b811580

DATABASE_HOST='rm-bp12ztip62mt13hu9.mysql.rds.aliyuncs.com'
DATABASE_PORT=3306
DATABASE_username='fe_cloud'
DATABASE_password='Fe_Cloud#123456#'
DATABASE_database='fe_cloud'


# DATABASE_HOST='rm-bp17sxod6w22gvhk2.mysql.rds.aliyuncs.com'
# DATABASE_PORT=3306
# DATABASE_username='fe_cloud'
# DATABASE_password='Fe_Cloud#123456#'
# DATABASE_database='fe_cloud_test'