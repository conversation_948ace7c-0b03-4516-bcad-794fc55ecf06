// src/test-record/test-record.controller.ts
import { Controller, Get, Post, Body, Patch, Param, Delete } from '@nestjs/common';
import { TestRecordService } from './record.service';
import { CreateTestRecordDto } from './dto/create-record.dto';

@Controller('test-records')
export class TestRecordController {
  constructor(private readonly service: TestRecordService) {}

  @Post()
  create(@Body() dto: CreateTestRecordDto) {
    return this.service.create(dto);
  }

  @Get()
  findAll() {
    return this.service.findAll();
  }

  @Get('/findByCaseName/:caseName')
  findResultByCaseName(@Param('caseName') caseName: string) {
    return this.service.findByCaseName(caseName);
  }

  @Get(':id')
  findOne(@Param('id') id: string) {
    return this.service.findOne(+id);
  }

  @Delete(':id')
  remove(@Param('id') id: string) {
    return this.service.remove(+id);
  }
}
