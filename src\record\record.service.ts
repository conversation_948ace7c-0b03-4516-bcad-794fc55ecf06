// src/test-record/test-record.service.ts
import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository,Like } from 'typeorm';
import { TestRecord } from './record.entity';
import { CreateTestRecordDto } from './dto/create-record.dto';

@Injectable()
export class TestRecordService {
  constructor(
    @InjectRepository(TestRecord)
    private readonly testRecordRepo: Repository<TestRecord>,
  ) {}

  async create(dto: CreateTestRecordDto) {
    return this.testRecordRepo.save(dto);
  }

  async findAll(): Promise<TestRecord[]> {
    return this.testRecordRepo.find();
  }

  async findOne(id: number) {
    return this.testRecordRepo.findOneBy({ id });
  }


  async remove(id: number): Promise<void> {
    await this.testRecordRepo.delete(id);
  }

  // 按用例名称搜索
async findByCaseName(name: string): Promise<TestRecord[]> {
  return this.testRecordRepo.find({ where: { caseName: Like(`%${name}%`) } });
}

// 按时间范围查询
async findByTimeRange(start: number, end: number): Promise<TestRecord[]> {
  return this.testRecordRepo
    .createQueryBuilder('record')
    .where('record.startTime BETWEEN :start AND :end', { start, end })
    .getMany();
}

}
