const { ClientFunction, Selector } = require('testcafe');

const getIframeUrl = ClientFunction(() => document.querySelector('iframe').src);
const getOrigin = ClientFunction(() => window.location.origin);

const willAuth = async (t) => {
  await t.wait(5000);
  let iframeUrl = await getIframeUrl.with({ boundTestRun: t })();
  if (!iframeUrl.startsWith('http')) {
    const origin = await getOrigin();
    iframeUrl = origin + iframeUrl;
  }
  const willAuthPage = await t.openWindow(iframeUrl);
  if (iframeUrl.includes('/ec-manage-web/authenticationPc')) {
    await t.wait(20000);
    // 天印意愿
    await t
      .click(Selector('.authenTypeItem').withText('短信认证'))
      .wait(500)
      .click(Selector('.verCodeBut'))
      .wait(1000)
      .typeText(Selector('.es-input__inner'), '123456', { replace: true })
      .wait(500)
      .click(Selector('.es-form-item__content .submit').withText('确认提交'))
      .wait(2000)
      .closeWindow(willAuthPage);
  } else {
    // saas意愿
    await t
      .click(Selector('button').withText('获取验证码'))
      .wait(1000)
      .typeText(Selector('[data-esign-inject-name="表单项验证码"]'), '123456', { replace: true })
      .wait(500)
      .click(Selector('[data-esign-inject-name="按钮确定"]'))
      .wait(2000)
      .closeWindow(willAuthPage);
  }
  await t.wait(2000);
}

exports.willAuth = willAuth