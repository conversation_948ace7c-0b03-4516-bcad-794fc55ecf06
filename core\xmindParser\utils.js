const isUndefined = i => i === undefined
const isNull = i => i === null
const isEmpty = i => i === ''

const getText = node => {
  try {
    return node.text
  } catch (e) {}
  return ''
}

const getChildrenNode = node => node?.children || []

const getChildNodeByIndex = (node, index) => {
  const children = getChildrenNode(node) || []
  if (children[index]) return children[index]
  return null
}

const getChildNodeTextByIndex = (node, index = 0) => {
  const childNode = getChildNodeByIndex(node, index)
  if (childNode) return getText(childNode)
  return ''
}

const getNode = (node, text) => {
  try {
    return node.children.find(child => child.text === text);
  } catch (e) {}
  return null
}

const getValue = (node, text) => getChildNodeTextByIndex(getNode(node, text))

module.exports = {
  isUndefined,
  isNull,
  isEmpty,
  getText,
  getChildNodeByIndex,
  getChildNodeTextByIndex,
  getNode,
  getValue,
  getChildrenNode
}