const fs = require('fs');
const path = require('path');
const ACTIONS = require(path.resolve(process.cwd(), './core/actions/index'))

let seed = 0

// const genTestcafeCases = (cases, file) => {
const genCases = ({ config, selector, cases }) => {
  const { base, mock, variable, name } = config

  const result = cases.map(testcase => {
    const { steps, title, variable: caseVariable, url = '' } = testcase;

    let executionBlock = `test('${title}', async t => {
      await t.wait(100);
      let elementSelector = ''
      let assertElementSelector = ''
      // url
      // actions
    });`;

    if (url) {
      executionBlock = executionBlock.replace('// url', `await t.navigateTo('${url}');`)
    }

    const selectorFormatter = (el, removeQuotes) => {
      if (!el) return '';
      let result = ''
      
      if (el.startsWith('css://')) {
        result = el.replace('css://', '')
      } else {
        result = `[data-esign-inject-name="${el}"]`
      }

      if (removeQuotes) return result
      return `'${result}'`
    };


    const toggleElement = (el, filterVisible = true) => {
      if (!el) return '';

      const tail = `${ filterVisible ? '.filterVisible()' : '' }\n`

      let res = ''
      
      if (el.startsWith('css://')) {
        const realSelector = el.replace('css://', '')
        res = ` elementSelector = Selector('${realSelector}')${tail}`;
      } else {
        const realSelector = `[data-esign-inject-name="${el}"]`
        res = ` elementSelector = Selector('${realSelector}')${tail}`;
      }

      return res
    };


    const toggleAssertElement = el => {
      if (!el) return '';
      if (el.startsWith('css://')) {
        return ` assertElementSelector = Selector('${el.replace('css://', '')}')\n`;
      }
      return ` assertElementSelector = Selector('[data-esign-inject-name="${el}"]')\n`;
    };

    function generateCombinedVariable(rule) {
      function generateRandomString(length, options = {}) {
        let result = '';
        const { letters, number, caseSensitive } = options;
        const lowerCaseLetters = 'abcdefghijklmnopqrstuvwxyz';
        const upperCaseLetters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ';
        const digits = '0123456789';
        
        let characters = '';
        if (letters) {
          characters += caseSensitive ? lowerCaseLetters + upperCaseLetters : lowerCaseLetters;
        }
        if (number) {
          characters += digits;
        }
    
        for (let i = 0; i < length; i++) {
          result += characters.charAt(Math.floor(Math.random() * characters.length));
        }
    
        return result;
      }
    
      let prefix = rule.prefix && rule.prefix.type === 'custom' ?
        generateRandomString(rule.prefix.length, rule.prefix) : '';
    
      let value = rule.value || '';
    
      let suffix = rule.suffix && rule.suffix.type === 'custom' ?
        generateRandomString(rule.suffix.length, rule.suffix) : '';
  
      return `${prefix}${value}${suffix}`;
    }

    const getVariable = (str) => {
      let res = str;
      (caseVariable || variable || []).forEach(item => {
        const { name } = item
        const key = '${' + name + '}'
        if (str.includes(key)) {
          res = res.replace(key, generateCombinedVariable(item))
        }
      })
      return res
    }

    const actions = steps.map((step, index) => {
      const stepDesc = `第${index + 1}步: ${step.desc}`
      let res = `\n// ${stepDesc}\n`
      if (step.type === 'click') {
        res += ACTIONS.base.click(selectorFormatter(step.element), step.options || '', step.ignore);
      } else if (step.type === 'rightClick') {
        res += ACTIONS.base.rightClick(selectorFormatter(step.element), step.ignore);
      } else if (step.type === 'doubleClick') {
        res += ACTIONS.base.doubleClick(selectorFormatter(step.element), step.ignore);
      } else if (step.type === 'input') {
        const inputValue = getVariable(step.value, step.ignore);
        res += ACTIONS.base.input(inputValue);
      } else if (step.type === 'keypress') {
        res += ACTIONS.base.keypress(step.value, step.ignore);
      } else if (step.type === 'refresh') {
        res += ACTIONS.base.refresh();
      } else if (step.type === 'resize') {
        res += ACTIONS.base.resize(step.value);
      } else if (step.type === 'choose') {
        res += ACTIONS.base.choose(step.value, step.ignore);
      } else if (step.type === 'debug') {
        res += ACTIONS.base.debug(step.value, step.ignore);
      } else if (step.type === 'hover') {
        res += ACTIONS.base.hover(selectorFormatter(step.element), step.ignore);
      } else if (step.type === 'wait') {
        res += ACTIONS.base.wait(step.value, step.ignore);
      } else if (step.type === 'drag') {
        if (step.value.type === 'absolute') {
          res += ACTIONS.utils.genIgnoreError(`${toggleElement(step.element)} ;const { x, y } = await getElementCenterFN(elementSelector); await t.drag(elementSelector, x - ${step.value.x}, y - ${step.value.y})`, step.ignore)
        } else if (step.value.type === 'relative') {
          res += ACTIONS.utils.genIgnoreError(`${toggleElement(step.element)} await t.drag(elementSelector, ${step.value.x}, ${step.value.y})`, step.ignore);
        }
      } else if (step.type === 'assertStyle') {
        const errorMsg = `样式断言失败 ${stepDesc}`
        res += ACTIONS.utils.genIgnoreError(`await actions.assert.style(t, ${selectorFormatter(step.element)}, ${JSON.stringify(step)});`, true, `await t.report('assert', '${errorMsg}')`);
      } else if (step.type === 'assertAttr') {
        const errorMsg = `属性断言失败 ${stepDesc}`
        res += ACTIONS.utils.genIgnoreError(`await actions.assert.attr(t, ${selectorFormatter(step.element)}, ${JSON.stringify(step)});`, true, `await t.report('assert', '${errorMsg}')`);
      } else if (step.type === 'assert') {
        const errorMsg = `断言失败 ${stepDesc}`
        res += ACTIONS.utils.genIgnoreError(`await actions.assert.base(t, ${selectorFormatter(step.element)}, ${JSON.stringify(step)});`, true, `await t.report('assert', '${errorMsg}')`);
      } else if (step.type === 'upload') {
        const variableName = `file_${++seed}`
        res += ACTIONS.utils.genIgnoreError(`const ${variableName} = await actions.composite.getUploadFilePath('${step.filePath || ''}');\n`, step.ignore);
        // res += `await actions.composite.upload(${selectorFormatter(step.element)});\n`
        res += ACTIONS.composite.upload(selectorFormatter(step.element), variableName);
        res += ACTIONS.base.wait(10000);
      } else if (step.type === 'datepicker') {
        res += ACTIONS.utils.genIgnoreError(`await datepickerFN(t, ${JSON.stringify(step.value)})`, step.ignore);
        // res += `await actions.composite.datepicker(t, ${JSON.stringify(step.value)})`
      } else if (step.type === 'setting') {
        res += `await actions.composite.setting(t, ${JSON.stringify(step.value)})`
      } else if (step.type === 'quickSetting') {
        res += `await actions.composite.quickSetting(t, ${JSON.stringify(step.value)})`
      } else if (step.type === 'compare') {
        step.value.selector = selectorFormatter(step.value.selector, true)
        res += `await actions.composite.compare(t, ${JSON.stringify(step.value)})`
      } else if (step.type === 'setWindowName') {
        res += `await actions.composite.setWindowName(t, ${JSON.stringify(step.value)}, windowWidth, windowHeight)`
      } else if (step.type === 'switchWindow') {
        res += `await actions.composite.switchWindow(t, ${JSON.stringify(step.value)})`
      } else if (step.type === 'closeWindow') {
        res += `await actions.composite.closeWindow(t, ${JSON.stringify(step.value)})`
      } else if (step.type === 'willAuth') {
        res += `await actions.composite.willAuth(t)`
      }
      return res;
    });

    return executionBlock.replace('// actions', actions.join('\n'));
  });

  const tempDirPath = path.resolve(process.cwd(), './tmp')
  const compareFileDirPath = path.resolve(process.cwd(), './compare/${name}')

  const casesStr = `
    const { Selector, ClientFunction } = require('testcafe');
    const path = require('path');

    const { requestInterceptionFN, getElementCenterFN, compareFN, datepickerFN } = require(path.resolve(process.cwd(), './core/specialMethod/index'))
    const actions = require(path.resolve(process.cwd(), './core/actions/index'))
    const { setCurrentElement, getCurrentElement, setAssertCurrentElement, getAssertCurrentElement } = require(path.resolve(process.cwd(), './core/currentElement/index'))

    const tempDirPath = "${tempDirPath}"
    const compareFileDirPath = "${compareFileDirPath}"

    const mock = ${JSON.stringify(mock)}
    const windowWidth = ${base.width}
    const windowHeight = ${base.height}
    fixture \`${name}\`
      .page \`${base.url}\`
      .requestHooks(requestInterceptionFN(mock?.request))
      .skipJsErrors();

      ${result.join('\n\n\n')}
    `;

  return casesStr;
};

exports.genCases = genCases