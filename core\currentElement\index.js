const { Selector } = require('testcafe')

let currentElement = null
let currentAssertElement = null

const setCurrentElement = async (selector, filterVisible = true, text = '') => {
  if (typeof selector === 'string') {
    currentElement = Selector(selector)
  
    if (filterVisible) currentElement = currentElement.filterVisible()
  
    if (text) currentElement = currentElement.withText(text)
  } else {
    currentElement = selector
  }

  return currentElement
}

const getCurrentElement = () => currentElement

const setAssertCurrentElement = async (t, selector) => {
  currentAssertElement = Selector(selector).with({ boundTestRun: t })
  return currentAssertElement
}

const getAssertCurrentElement = () => currentAssertElement

exports.setCurrentElement = setCurrentElement
exports.getCurrentElement = getCurrentElement
exports.setAssertCurrentElement = setAssertCurrentElement
exports.getAssertCurrentElement = getAssertCurrentElement