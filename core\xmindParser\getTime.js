const moment = require('moment');

const isAbsoluteTime = (text) => {
  // 检查是否为绝对时间描述
  return /^\d{4}-\d{2}-\d{2}(\s\d{2}:\d{2}:\d{2})?$/.test(text.split('至')[0].trim());
}

const parseSingleTime = (text, referenceDate) => {
  let m = moment(referenceDate);
  
  // 处理相对时间描述
  if (text.includes('天后')) {
    const days = parseInt(text.match(/\d+/)[0], 10);
    m.add(days, 'days');
  } else if (text.includes('天前')) {
    const days = parseInt(text.match(/\d+/)[0], 10);
    m.subtract(days, 'days');
  } else if (text.includes('月前')) {
    const months = parseInt(text.match(/\d+/)[0], 10);
    m.subtract(months, 'months');
  } else if (text.includes('月后')) {
    const months = parseInt(text.match(/\d+/)[0], 10);
    m.add(months, 'months');
  } else if (text.includes('年前')) {
    const years = parseInt(text.match(/\d+/)[0], 10);
    m.subtract(years, 'years');
  } else if (text.includes('年后')) {
    const years = parseInt(text.match(/\d+/)[0], 10);
    m.add(years, 'years');
  }

  // 处理指定时间部分
  if (text.includes('的')) {
    const timePart = text.split('的')[1].trim();
    const [hours, minutes, seconds] = timePart.split(':').map(Number);
    m.set({ hour: hours, minute: minutes, second: seconds });
  }

  return m;
}

const getTime = (text) => {
  const now = new Date();
  const isAbsolute = isAbsoluteTime(text)
  const result = {
    type: isAbsolute ? 'absolute' : 'relative',
  }
  // 解析文本中的时间信息
  if (text.includes('至')) {
    // 处理时间范围的情况
    const [startText, endText] = text.split('至').map(t => t.trim());
    result.value = [
      parseSingleTime(startText, now),
      parseSingleTime(endText, now)
    ].map(time => isAbsolute ? time.valueOf() : time.diff(now));
  } else {
    // 单个时间点的情况
    const time = parseSingleTime(text, now);
    result.value = [isAbsolute ? time.valueOf() : time.diff(now)]
  }

  return result
}

exports.getTime = getTime